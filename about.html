<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About - Aquaknow Fish Identification Platform</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <h1>🐠 Aquaknow</h1>
                    <p>Fish Identification & Education</p>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="index.html#fish-list" class="nav-link">Fish List</a></li>
                    <li><a href="education.html" class="nav-link">Education</a></li>
                    <li><a href="about.html" class="nav-link active">About</a></li>
                </ul>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- About Hero Section -->
    <section class="about-hero">
        <div class="container">
            <div class="about-hero-content">
                <h1>🌊 About Aquaknow</h1>
                <p>Discover the fascinating world of marine life through our comprehensive fish identification and education platform</p>
            </div>
        </div>
    </section>

    <!-- Mission Section -->
    <section class="mission-section">
        <div class="container">
            <div class="mission-content">
                <div class="mission-text">
                    <h2>🎯 Our Mission</h2>
                    <p>At Aquaknow, we are passionate about marine education and fish identification. Our mission is to provide an accessible, comprehensive platform that helps people learn about different fish species, their habitats, and their importance in our ecosystem.</p>

                    <p>We believe that understanding marine life is crucial for conservation efforts and sustainable fishing practices. Through detailed information, beautiful imagery, and educational content, we aim to foster a deeper appreciation for the ocean's treasures.</p>

                    <h3>🌟 What We Offer</h3>
                    <ul class="mission-list">
                        <li>Comprehensive fish species database with detailed information</li>
                        <li>High-quality images for accurate identification</li>
                        <li>Educational content about fish habitats, diet, and behavior</li>
                        <li>Nutritional information and cooking tips</li>
                        <li>Conservation status and environmental awareness</li>
                    </ul>
                </div>
                <div class="mission-visual">
                    <div class="mission-icon">🐠</div>
                    <div class="mission-stats">
                        <div class="stat-item">
                            <div class="stat-number">10+</div>
                            <div class="stat-label">Fish Species</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">Accurate Info</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">24/7</div>
                            <div class="stat-label">Access</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="team-section">
        <div class="container">
            <h2>👥 Our Team</h2>
            <p class="section-subtitle">Meet the passionate developers behind Aquaknow</p>

            <div class="team-grid">
                <div class="team-card">
                    <div class="team-avatar">👨‍💻</div>
                    <h3>Colongon</h3>
                    <p class="team-role">Co-Developer & Marine Enthusiast</p>
                    <p>Passionate about marine biology and web development, bringing technical expertise to create an intuitive fish identification platform.</p>
                </div>

                <div class="team-card">
                    <div class="team-avatar">👩‍💻</div>
                    <h3>Ramos</h3>
                    <p class="team-role">Co-Developer & Education Specialist</p>
                    <p>Dedicated to making marine education accessible to everyone through innovative design and comprehensive educational content.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Values Section with Card Navigation -->
    <section class="values-section">
        <div class="container">
            <h2>💎 Our Values</h2>
            <div class="card-navigation-container">
                <button class="nav-arrow nav-arrow-left" onclick="navigateCards('values', -1)">
                    <span>‹</span>
                </button>

                <div class="values-carousel">
                    <div class="values-grid" id="valuesGrid">
                        <div class="value-card active" data-index="0">
                            <div class="value-icon">🎓</div>
                            <h3>Education First</h3>
                            <p>We prioritize accurate, comprehensive educational content that helps users learn about marine life and conservation.</p>
                            <div class="card-footer">
                                <a href="education.html" class="card-link">Learn More →</a>
                            </div>
                        </div>

                        <div class="value-card" data-index="1">
                            <div class="value-icon">🌍</div>
                            <h3>Conservation</h3>
                            <p>We promote awareness about marine conservation and sustainable fishing practices for future generations.</p>
                            <div class="card-footer">
                                <a href="education.html#conservation-section" class="card-link">Learn More →</a>
                            </div>
                        </div>

                        <div class="value-card" data-index="2">
                            <div class="value-icon">🔍</div>
                            <h3>Accuracy</h3>
                            <p>All our fish information is carefully researched and verified to ensure users receive reliable, scientific data.</p>
                            <div class="card-footer">
                                <a href="index.html#fish-list" class="card-link">View Fish Database →</a>
                            </div>
                        </div>

                        <div class="value-card" data-index="3">
                            <div class="value-icon">🤝</div>
                            <h3>Accessibility</h3>
                            <p>We believe marine education should be accessible to everyone, regardless of their background or experience level.</p>
                            <div class="card-footer">
                                <a href="#contact-form" class="card-link">Contact Us →</a>
                            </div>
                        </div>
                    </div>
                </div>

                <button class="nav-arrow nav-arrow-right" onclick="navigateCards('values', 1)">
                    <span>›</span>
                </button>
            </div>

            <!-- Card Indicators -->
            <div class="card-indicators">
                <span class="indicator active" onclick="goToCard('values', 0)"></span>
                <span class="indicator" onclick="goToCard('values', 1)"></span>
                <span class="indicator" onclick="goToCard('values', 2)"></span>
                <span class="indicator" onclick="goToCard('values', 3)"></span>
            </div>
        </div>
    </section>

    <!-- Fish Species Quick Access -->
    <section class="fish-quick-access">
        <div class="container">
            <h2>🐠 Quick Fish Species Access</h2>
            <div class="search-container">
                <input type="text" id="fishSearch" placeholder="Search for fish species..." onkeyup="searchFish()">
                <button class="search-btn" onclick="searchFish()">🔍</button>
            </div>

            <div class="fish-quick-grid" id="fishQuickGrid">
                <div class="quick-fish-card" onclick="goToFish('tilapia')">
                    <div class="quick-fish-icon">🐟</div>
                    <h4>Tilapia</h4>
                    <p>Freshwater</p>
                </div>
                <div class="quick-fish-card" onclick="goToFish('milkfish')">
                    <div class="quick-fish-icon">🐠</div>
                    <h4>Milkfish</h4>
                    <p>Anadromous</p>
                </div>
                <div class="quick-fish-card" onclick="goToFish('tuna')">
                    <div class="quick-fish-icon">🐟</div>
                    <h4>Tuna</h4>
                    <p>Marine</p>
                </div>
                <div class="quick-fish-card" onclick="goToFish('salmon')">
                    <div class="quick-fish-icon">🐠</div>
                    <h4>Salmon</h4>
                    <p>Anadromous</p>
                </div>
                <div class="quick-fish-card" onclick="goToFish('lapu-lapu')">
                    <div class="quick-fish-icon">🐟</div>
                    <h4>Lapu-Lapu</h4>
                    <p>Marine</p>
                </div>
                <div class="quick-fish-card" onclick="goToFish('mackerel')">
                    <div class="quick-fish-icon">🐠</div>
                    <h4>Mackerel</h4>
                    <p>Marine</p>
                </div>
            </div>

            <div class="view-all-fish">
                <a href="index.html#fish-list" class="cta-button">View All Fish Species</a>
            </div>
        </div>
    </section>

    <!-- Contact Section with Form -->
    <section class="contact-section" id="contact-form">
        <div class="container">
            <h2>📞 Get In Touch</h2>
            <p class="section-subtitle">Have questions or suggestions? We'd love to hear from you!</p>

            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <h3>Email</h3>
                        <p><EMAIL></p>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">📱</div>
                        <h3>Phone</h3>
                        <p>09123456789</p>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">🌐</div>
                        <h3>Platform</h3>
                        <p>Available 24/7 online</p>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="contact-form-container">
                    <h3>Send us a Message</h3>
                    <form class="contact-form" onsubmit="submitContactForm(event)">
                        <div class="form-group">
                            <label for="name">Name *</label>
                            <input type="text" id="name" name="name" required>
                        </div>

                        <div class="form-group">
                            <label for="email">Email *</label>
                            <input type="email" id="email" name="email" required>
                        </div>

                        <div class="form-group">
                            <label for="subject">Subject</label>
                            <select id="subject" name="subject">
                                <option value="general">General Inquiry</option>
                                <option value="fish-info">Fish Information</option>
                                <option value="suggestion">Suggestion</option>
                                <option value="bug-report">Bug Report</option>
                                <option value="partnership">Partnership</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="message">Message *</label>
                            <textarea id="message" name="message" rows="5" required placeholder="Tell us about your inquiry..."></textarea>
                        </div>

                        <button type="submit" class="submit-btn">
                            <span class="btn-text">Send Message</span>
                            <span class="btn-icon">📤</span>
                        </button>
                    </form>

                    <div class="form-success" id="formSuccess" style="display: none;">
                        <div class="success-icon">✅</div>
                        <h4>Message Sent Successfully!</h4>
                        <p>Thank you for contacting us. We'll get back to you soon!</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>🐠 Aquaknow</h3>
                    <p>Your trusted platform for fish identification and marine education.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="index.html#fish-list">Fish List</a></li>
                        <li><a href="education.html">Education</a></li>
                        <li><a href="about.html">About</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: 09123456789</p>
                </div>
            </div>
        </div>
        <div class="developer-credits">
            <p>&copy; 2025 Aquaknow Fish Identification Platform. All rights reserved.<br>
            Created and Developed by Colongon and Ramos</p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
