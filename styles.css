/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: #1a202c;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    overflow-x: hidden;
    font-weight: 400;
}

/* Modern Beach Theme Variables */
:root {
    --ocean-blue: #2563eb;
    --deep-ocean: #1e293b;
    --coral-pink: #f472b6;
    --sandy-beige: #fbbf24;
    --sea-foam: #10b981;
    --pearl-white: #ffffff;
    --sunset-orange: #f97316;
    --tropical-teal: #06b6d4;
    --soft-blue: #dbeafe;
    --warm-sand: #fef3c7;
    --coral-light: #fce7f3;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border-bottom: 1px solid rgba(37, 99, 235, 0.1);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo h1 {
    font-family: 'Playfair Display', serif;
    background: linear-gradient(135deg, var(--ocean-blue), var(--tropical-teal));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: -5px;
    letter-spacing: -0.02em;
}

.logo p {
    color: var(--deep-ocean);
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.8;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--deep-ocean);
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    padding: 12px 20px;
    border-radius: 12px;
    font-size: 0.95rem;
}

.nav-link:hover {
    color: var(--ocean-blue);
    background: var(--soft-blue);
    transform: translateY(-1px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--ocean-blue), var(--tropical-teal));
    transition: width 0.3s ease;
    border-radius: 1px;
}

.nav-link:hover::after {
    width: 60%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--deep-ocean);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Hero Section */
.hero {
    height: 100vh;
    background:
        radial-gradient(circle at 20% 80%, rgba(253, 203, 110, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(253, 121, 168, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 206, 201, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #00b894 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="coral" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(253,121,168,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23coral)"/></svg>');
    animation: float 20s ease-in-out infinite;
    opacity: 0.3;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    z-index: 2;
    max-width: 600px;
    padding: 0 20px;
}

.hero h1 {
    font-family: 'Playfair Display', serif;
    font-size: 4.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: white;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.02em;
    line-height: 1.1;
}

.hero p {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    color: white;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    font-weight: 400;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    display: inline-block;
    background: linear-gradient(135deg, var(--ocean-blue), var(--tropical-teal));
    color: white;
    padding: 16px 32px;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(37, 99, 235, 0.4);
    background: linear-gradient(135deg, var(--tropical-teal), var(--sea-foam));
}



/* Fish Section */
.fish-section {
    padding: 100px 0;
    background:
        radial-gradient(circle at 10% 20%, rgba(0, 206, 201, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(253, 203, 110, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, var(--pearl-white) 0%, #e8f4fd 100%);
    position: relative;
}

.fish-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="shells" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="2" fill="rgba(0,206,201,0.05)"/><circle cx="5" cy="25" r="1" fill="rgba(253,203,110,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23shells)"/></svg>');
    opacity: 0.3;
    animation: drift 30s linear infinite;
}

@keyframes drift {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(-30px) translateY(-30px); }
}

.fish-section h2 {
    font-family: 'Playfair Display', serif;
    text-align: center;
    font-size: 3.5rem;
    color: var(--deep-ocean);
    margin-bottom: 1rem;
    font-weight: 700;
    position: relative;
    z-index: 2;
    letter-spacing: -0.02em;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: var(--deep-ocean);
    margin-bottom: 3rem;
    opacity: 0.7;
    position: relative;
    z-index: 2;
}

.fish-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.fish-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(37, 99, 235, 0.1);
    position: relative;
    z-index: 2;
}

.fish-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(37, 99, 235, 0.15);
    border-color: var(--ocean-blue);
}

.fish-image {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.fish-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.fish-card:hover .fish-image img {
    transform: scale(1.1);
}

.fish-info {
    padding: 1.5rem;
}

.fish-info h3 {
    color: var(--deep-ocean);
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
    font-family: 'Playfair Display', serif;
}

.scientific-name {
    color: var(--ocean-blue);
    font-style: italic;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.fish-description {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.learn-more-btn {
    background: linear-gradient(135deg, var(--ocean-blue), var(--tropical-teal));
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.learn-more-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
    background: linear-gradient(135deg, var(--tropical-teal), var(--sea-foam));
}

/* View All Fish Section */
.view-all-section {
    margin-top: 3rem;
    text-align: center;
    position: relative;
    z-index: 2;
}

.view-all-btn {
    background: linear-gradient(135deg, var(--ocean-blue), var(--tropical-teal));
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 12px;
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.view-all-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(37, 99, 235, 0.4);
    background: linear-gradient(135deg, var(--tropical-teal), var(--sea-foam));
}

/* Education Preview Section */
.education-preview {
    padding: 120px 0;
    background:
        radial-gradient(circle at 30% 70%, rgba(253, 121, 168, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(0, 206, 201, 0.15) 0%, transparent 50%),
        linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #00b894 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.education-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="bubbles" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="5" cy="20" r="0.8" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23bubbles)"/></svg>');
    animation: bubble-float 25s linear infinite;
    opacity: 0.4;
}

@keyframes bubble-float {
    0% { transform: translateY(0); }
    100% { transform: translateY(-25px); }
}

.education-preview h2 {
    font-family: 'Playfair Display', serif;
    text-align: center;
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
    position: relative;
    z-index: 2;
    color: white;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.02em;
}

.education-preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
    margin-top: 3rem;
    position: relative;
    z-index: 2;
}

.education-preview-card {
    cursor: pointer;
    transition: all 0.4s ease;
    position: relative;
}

.card-background {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    padding: 2.5rem;
    border-radius: 25px;
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.card-background::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.education-preview-card:hover .card-background::before {
    opacity: 1;
    animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.education-preview-card:hover {
    transform: translateY(-15px) scale(1.02);
}

.education-preview-card:hover .card-background {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(253, 203, 110, 0.6);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.education-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
}

.education-preview-card h3 {
    font-size: 1.6rem;
    margin-bottom: 1rem;
    font-weight: 700;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.education-preview-card p {
    opacity: 0.9;
    line-height: 1.6;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.card-decorations {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.card-decorations span {
    font-size: 1.5rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.education-preview-card:hover .card-decorations span {
    opacity: 1;
    transform: scale(1.2);
}

.education-cta {
    text-align: center;
    margin-top: 4rem;
    position: relative;
    z-index: 2;
}

.education-btn {
    display: inline-block;
    background: linear-gradient(45deg, var(--sandy-beige), var(--sunset-orange), var(--coral-pink));
    color: white;
    padding: 20px 45px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.2rem;
    transition: all 0.4s ease;
    box-shadow: 0 10px 30px rgba(253, 203, 110, 0.4);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.education-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.education-btn:hover::before {
    left: 100%;
}

.education-btn:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 20px 40px rgba(253, 203, 110, 0.6);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, var(--deep-ocean) 0%, #1a1a2e 100%);
    color: white;
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: #4ecdc4;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.footer-section h4 {
    color: #4ecdc4;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #4ecdc4;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #333;
    color: #ccc;
}

.developer-credits {
    background: linear-gradient(135deg, var(--ocean-blue), var(--tropical-teal));
    color: white;
    padding: 15px 0;
    text-align: center;
    font-weight: 600;
    font-size: 0.95rem;
    border-top: 2px solid rgba(255, 255, 255, 0.1);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 16px;
    width: 95%;
    max-width: 900px;
    max-height: 95vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.4s ease;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

@keyframes modalSlideIn {
    from { transform: translateY(-30px) scale(0.95); opacity: 0; }
    to { transform: translateY(0) scale(1); opacity: 1; }
}

.close {
    color: white;
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 15px;
    z-index: 10;
    transition: all 0.3s ease;
}

.close:hover {
    background: var(--ocean-blue);
    transform: scale(1.1);
}

.modal-fish-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 16px 16px 0 0;
}

.modal-fish-content {
    padding: 2rem;
}

.modal-fish-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    color: var(--deep-ocean);
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.modal-scientific-name {
    color: var(--ocean-blue);
    font-style: italic;
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.8;
}

.fish-detail-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--soft-blue);
    border-radius: 12px;
    border-left: 4px solid var(--ocean-blue);
}

.fish-detail-section h3 {
    color: var(--deep-ocean);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.fish-detail-section p, .fish-detail-section li {
    color: #64748b;
    line-height: 1.7;
    font-size: 1rem;
}

.fish-detail-section ul {
    padding-left: 1.5rem;
}

.fish-detail-section li {
    margin-bottom: 0.8rem;
}

.fish-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.stat-item {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    border: 2px solid var(--soft-blue);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ocean-blue);
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: #64748b;
    margin-top: 0.5rem;
}

/* About Page Styles */
.about-hero {
    padding: 150px 0 100px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.about-hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.about-hero-content p {
    font-size: 1.3rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.mission-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.mission-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.mission-text h2 {
    font-size: 2.5rem;
    color: #2c5aa0;
    margin-bottom: 2rem;
    font-weight: 700;
}

.mission-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
    margin-bottom: 1.5rem;
}

.mission-text h3 {
    font-size: 1.8rem;
    color: #2c5aa0;
    margin: 2rem 0 1rem 0;
    font-weight: 600;
}

.mission-list {
    list-style: none;
    padding: 0;
}

.mission-list li {
    margin: 0.8rem 0;
    padding-left: 1.5rem;
    position: relative;
    color: #555;
    line-height: 1.6;
}

.mission-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #2c5aa0;
    font-weight: bold;
}

.mission-visual {
    text-align: center;
}

.mission-icon {
    font-size: 6rem;
    margin-bottom: 2rem;
    opacity: 0.8;
}

.mission-stats {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.stat-item {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2c5aa0;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

/* Team Section Styles */
.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.team-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.team-card:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.25);
}

.team-avatar {
    font-size: 4rem;
    margin-bottom: 1.5rem;
}

.team-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.team-role {
    color: rgba(255, 255, 255, 0.8);
    font-style: italic;
    margin-bottom: 1rem;
    font-weight: 500;
}

.team-card p {
    opacity: 0.9;
    line-height: 1.6;
}

/* Values Section */
.values-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.values-section h2 {
    text-align: center;
    font-size: 3rem;
    color: #2c5aa0;
    margin-bottom: 3rem;
    font-weight: 700;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.value-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.value-card:hover {
    transform: translateY(-8px);
    border-color: #2c5aa0;
    box-shadow: 0 15px 35px rgba(44, 90, 160, 0.2);
}

.value-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.value-card h3 {
    color: #2c5aa0;
    font-size: 1.4rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.value-card p {
    color: #666;
    line-height: 1.6;
}

.ocean-graphic {
    font-size: 8rem;
    text-align: center;
    opacity: 0.7;
}

.features-section {
    padding: 100px 0;
    background: white;
}

.features-section h2 {
    text-align: center;
    font-size: 3rem;
    color: #2c5aa0;
    margin-bottom: 3rem;
    font-weight: 700;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(44, 90, 160, 0.1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    color: #2c5aa0;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

.team-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.team-section h2 {
    text-align: center;
    font-size: 3rem;
    margin-bottom: 3rem;
    font-weight: 700;
}

.commitment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.commitment-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.commitment-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.commitment-card p {
    opacity: 0.9;
    line-height: 1.6;
}

.contact-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.contact-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.contact-content h2 {
    font-size: 3rem;
    color: #2c5aa0;
    margin-bottom: 1rem;
    font-weight: 700;
}

.contact-content > p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 3rem;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.contact-icon {
    font-size: 2rem;
    min-width: 50px;
}

.contact-item h4 {
    color: #2c5aa0;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.contact-item p {
    color: #666;
    margin: 0;
}

.cta-section {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.cta-section h3 {
    color: #2c5aa0;
    font-size: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.cta-section p {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(255, 255, 255, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero h1, .about-hero-content h1 {
        font-size: 2.5rem;
    }

    .fish-grid, .features-grid, .commitment-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .mission-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-info {
        grid-template-columns: 1fr;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
    }

    .categories-grid, .anatomy-grid, .nutrition-grid {
        grid-template-columns: 1fr;
    }

    .conservation-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .education-hero-content h1 {
        font-size: 2.5rem;
    }

    .anatomy-section h2, .nutrition-section h2, .conservation-section h2 {
        font-size: 2.5rem;
    }

    .team-grid, .values-grid {
        grid-template-columns: 1fr;
    }

    .mission-stats {
        flex-direction: row;
        justify-content: space-around;
    }

    .stat-item {
        flex: 1;
        margin: 0 0.5rem;
    }
}

/* Education Pages Styles */
.education-hero {
    padding: 150px 0 100px;
    background:
        radial-gradient(circle at 20% 80%, rgba(253, 203, 110, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(253, 121, 168, 0.3) 0%, transparent 50%),
        linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #00b894 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.education-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="seaweed" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M10,0 Q15,10 10,20" stroke="rgba(0,206,201,0.1)" stroke-width="2" fill="none"/></pattern></defs><rect width="100" height="100" fill="url(%23seaweed)"/></svg>');
    animation: sway 15s ease-in-out infinite;
    opacity: 0.3;
}

@keyframes sway {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(10px); }
}

.education-hero-content h1 {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
    filter: drop-shadow(0 0 20px rgba(253, 203, 110, 0.8));
    position: relative;
    z-index: 2;
}

.education-hero-content p {
    font-size: 1.4rem;
    opacity: 0.95;
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

.hero-decorations {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    position: relative;
    z-index: 2;
}

.decoration {
    font-size: 2rem;
    animation: bob 3s ease-in-out infinite;
}

.decoration:nth-child(2) {
    animation-delay: 1s;
}

.decoration:nth-child(3) {
    animation-delay: 2s;
}

@keyframes bob {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.education-categories {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--pearl-white) 0%, #e8f4fd 100%);
}

.education-categories h2 {
    text-align: center;
    font-size: 3.5rem;
    background: linear-gradient(45deg, var(--ocean-blue), var(--tropical-teal), var(--sea-foam));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 3rem;
    font-weight: 800;
    filter: drop-shadow(0 0 10px rgba(0, 184, 148, 0.3));
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
}

.category-card {
    background: white;
    border-radius: 25px;
    padding: 2.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.4s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(116, 185, 255, 0.05), rgba(0, 184, 148, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-card:hover::before {
    opacity: 1;
}

.category-card:hover {
    transform: translateY(-15px) scale(1.02);
    border-color: var(--coral-pink);
    box-shadow: 0 20px 40px rgba(253, 121, 168, 0.2);
}

.category-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    filter: drop-shadow(0 0 10px rgba(0, 184, 148, 0.3));
}

.category-card h3 {
    color: var(--ocean-blue);
    font-size: 1.8rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.category-card p {
    color: #666;
    line-height: 1.6;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.card-decoration {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.card-decoration span {
    font-size: 1.5rem;
    opacity: 0.6;
    transition: all 0.3s ease;
}

.category-card:hover .card-decoration span {
    opacity: 1;
    transform: scale(1.2);
}

.category-header {
    margin-bottom: 1.5rem;
}

.category-content {
    text-align: left;
}

.fish-examples {
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(116, 185, 255, 0.1);
    border-radius: 10px;
}

.fish-examples h4 {
    color: var(--ocean-blue);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.fish-examples ul {
    list-style: none;
    padding: 0;
}

.fish-examples li {
    margin: 0.5rem 0;
    padding-left: 1rem;
    position: relative;
}

.fish-examples li::before {
    content: "🐟";
    position: absolute;
    left: 0;
}

.category-facts {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(0, 206, 201, 0.1);
    border-radius: 10px;
    border-left: 4px solid var(--tropical-teal);
}

.category-facts h4 {
    color: var(--tropical-teal);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

/* Anatomy Section */
.anatomy-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--warm-sand) 0%, var(--coral-light) 100%);
}

.anatomy-section h2 {
    text-align: center;
    font-size: 3.5rem;
    background: linear-gradient(45deg, var(--ocean-blue), var(--coral-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 3rem;
    font-weight: 800;
}

.anatomy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.anatomy-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.anatomy-card:hover {
    transform: translateY(-8px);
    border-color: var(--coral-pink);
    box-shadow: 0 15px 35px rgba(253, 121, 168, 0.2);
}

.anatomy-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.anatomy-card h3 {
    color: var(--deep-ocean);
    font-size: 1.4rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.anatomy-card p {
    color: #666;
    line-height: 1.6;
}

/* Nutrition Section */
.nutrition-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--soft-blue) 0%, var(--pearl-white) 100%);
}

.nutrition-section h2 {
    text-align: center;
    font-size: 3.5rem;
    background: linear-gradient(45deg, var(--sea-foam), var(--sandy-beige));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 3rem;
    font-weight: 800;
}

.nutrition-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.nutrition-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.nutrition-card:hover {
    transform: translateY(-8px);
    border-color: var(--sea-foam);
    box-shadow: 0 15px 35px rgba(16, 185, 129, 0.2);
}

.nutrition-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.nutrition-card h3 {
    color: var(--deep-ocean);
    font-size: 1.4rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.nutrition-card p {
    color: #666;
    line-height: 1.6;
}

/* Conservation Section */
.conservation-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--pearl-white) 0%, var(--warm-sand) 100%);
}

.conservation-section h2 {
    text-align: center;
    font-size: 3.5rem;
    background: linear-gradient(45deg, var(--ocean-blue), var(--sea-foam));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 3rem;
    font-weight: 800;
}

.conservation-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.conservation-text h3 {
    color: var(--deep-ocean);
    font-size: 2rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.conservation-text h4 {
    color: var(--ocean-blue);
    font-size: 1.4rem;
    margin: 2rem 0 1rem 0;
    font-weight: 600;
}

.conservation-text p {
    color: #666;
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.conservation-text ul {
    color: #666;
    line-height: 1.7;
    margin-left: 1.5rem;
    margin-bottom: 1.5rem;
}

.conservation-tips h3 {
    color: var(--deep-ocean);
    font-size: 1.8rem;
    margin-bottom: 2rem;
    font-weight: 700;
    text-align: center;
}

.tip-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--sea-foam);
}

.tip-card h4 {
    color: var(--ocean-blue);
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.tip-card p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

.featured-learning {
    padding: 100px 0;
    background:
        radial-gradient(circle at 30% 70%, rgba(253, 121, 168, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(0, 206, 201, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #00b894 100%);
    color: white;
}

.featured-learning h2 {
    text-align: center;
    font-size: 3.5rem;
    margin-bottom: 3rem;
    font-weight: 800;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
}

.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
}

.featured-item {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    padding: 2.5rem;
    border-radius: 25px;
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
}

.featured-item:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.25);
    border-color: var(--sandy-beige);
}

.featured-image {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
}

.featured-item h3 {
    font-size: 1.6rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.featured-item p {
    opacity: 0.9;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.learn-btn {
    display: inline-block;
    background: linear-gradient(45deg, var(--sandy-beige), var(--coral-pink));
    color: white;
    padding: 12px 25px;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(253, 203, 110, 0.3);
}

.learn-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(253, 203, 110, 0.5);
}

.quiz-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--pearl-white) 0%, #e8f4fd 100%);
    text-align: center;
}

.quiz-content h2 {
    font-size: 3rem;
    background: linear-gradient(45deg, var(--ocean-blue), var(--tropical-teal));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    font-weight: 800;
}

.quiz-content p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 3rem;
}

.quiz-options {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.quiz-btn {
    background: linear-gradient(45deg, var(--ocean-blue), var(--sea-foam));
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(9, 132, 227, 0.3);
    display: flex;
    align-items: center;
    gap: 10px;
}

.quiz-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(9, 132, 227, 0.4);
}

.quiz-icon {
    font-size: 1.3rem;
}

/* Simple Education Section */
.simple-education {
    padding: 150px 0 100px;
    background: linear-gradient(135deg, var(--soft-blue) 0%, var(--coral-light) 100%);
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.education-redirect {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    padding: 3rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.education-redirect h1 {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    color: var(--deep-ocean);
    margin-bottom: 2rem;
    font-weight: 700;
}

.education-redirect p {
    color: #64748b;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.back-to-fish-btn {
    display: inline-block;
    background: linear-gradient(135deg, var(--ocean-blue), var(--tropical-teal));
    color: white;
    padding: 16px 32px;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    margin-top: 1rem;
}

.back-to-fish-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(37, 99, 235, 0.4);
    background: linear-gradient(135deg, var(--tropical-teal), var(--sea-foam));
}
