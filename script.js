// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const modal = document.getElementById('fishModal');
    const closeModal = document.querySelector('.close');

    // Mobile menu toggle
    hamburger.addEventListener('click', function() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Modal functionality
    closeModal.addEventListener('click', function() {
        modal.style.display = 'none';
    });

    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });

    // Header scroll effect
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
        }
    });

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe fish cards for animation
    document.querySelectorAll('.fish-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Observe education cards for animation
    document.querySelectorAll('.education-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});

// Fish details data with accurate information
const fishData = {
    tilapia: {
        name: "Tilapia",
        scientificName: "Oreochromis niloticus",
        image: "images/tilapia.jpg",
        habitat: "Freshwater lakes, rivers, ponds, and aquaculture farms",
        diet: "Omnivorous - algae, aquatic plants, small invertebrates, and detritus",
        size: "20-35 cm (8-14 inches)",
        weight: "0.5-2 kg (1-4 lbs)",
        lifespan: "6-10 years",
        waterTemp: "20-30°C (68-86°F)",
        nutrition: "High in protein (26g per 100g), low in fat, rich in phosphorus, selenium, and vitamin B12",
        facts: [
            "Native to Africa and the Middle East, now farmed in over 100 countries",
            "Can survive in both fresh and brackish water",
            "Third most important fish in aquaculture after carp and salmon",
            "Fast-growing and disease-resistant, reaching market size in 6-8 months",
            "Can tolerate low oxygen levels and poor water quality",
            "Excellent source of lean protein with mild, sweet flavor"
        ],
        conservation: "Least Concern - widely farmed and sustainable when properly managed",
        cookingTips: "Best grilled, fried, or steamed. Mild flavor pairs well with Asian and Latin seasonings."
    },
    milkfish: {
        name: "Milkfish (Bangus)",
        scientificName: "Chanos chanos",
        image: "images/milkfish.jpg",
        habitat: "Coastal waters, estuaries, mangroves, and freshwater ponds",
        diet: "Herbivorous - algae, soft aquatic plants, small invertebrates, and plankton",
        size: "100-180 cm (3-6 feet)",
        weight: "3-14 kg (6-30 lbs)",
        lifespan: "15-20 years",
        waterTemp: "26-32°C (79-90°F)",
        nutrition: "High in protein (20g per 100g), omega-3 fatty acids, vitamin B12, and selenium",
        facts: [
            "National fish of the Philippines and important cultural symbol",
            "Anadromous - can migrate between saltwater and freshwater",
            "One of the fastest-growing fish species in aquaculture",
            "Has a distinctive silvery, torpedo-shaped body with forked tail",
            "Can reach speeds up to 60 km/h when escaping predators",
            "Eyes are covered by transparent adipose tissue (fat layer)",
            "Highly prized for its delicate, milky white flesh"
        ],
        conservation: "Least Concern - sustainably farmed across Southeast Asia",
        cookingTips: "Popular grilled, fried, or in soups. Remove many small bones carefully. Best marinated in vinegar or calamansi."
    },
    "lapu-lapu": {
        name: "Lapu-Lapu (Grouper)",
        scientificName: "Epinephelus spp.",
        image: "images/lapu-lapu.jpg",
        habitat: "Coral reefs, rocky coastal areas, and deep waters up to 200m",
        diet: "Carnivorous - fish, crustaceans, cephalopods, and smaller reef fish",
        size: "30-150 cm (1-5 feet) depending on species",
        weight: "2-50 kg (4-110 lbs)",
        lifespan: "15-40+ years",
        waterTemp: "24-28°C (75-82°F)",
        nutrition: "Excellent source of lean protein (24g per 100g), low in mercury, rich in selenium and vitamin D",
        facts: [
            "Named after Filipino hero Lapu-Lapu who defeated Magellan",
            "Highly prized for their firm, white, flaky meat with mild flavor",
            "Important apex predators that help maintain reef ecosystem balance",
            "Many species are protogynous hermaphrodites (change from female to male)",
            "Can live over 40 years and grow very large",
            "Ambush predators that swallow prey whole with powerful suction"
        ],
        conservation: "Varies by species - many are overfished and need protection due to slow growth",
        cookingTips: "Excellent steamed, grilled, or in soups. Sweet, delicate flavor. Popular in Chinese and Filipino cuisine."
    },
    dalag: {
        name: "Dalag (Snakehead)",
        scientificName: "Channa striata",
        image: "images/Dalag (Snakehead).jpg",
        habitat: "Freshwater swamps, rivers, rice fields, and shallow ponds",
        diet: "Carnivorous - fish, frogs, aquatic insects, and small crustaceans",
        size: "25-100 cm (10-40 inches)",
        weight: "1-7 kg (2-15 lbs)",
        lifespan: "8-15 years",
        waterTemp: "22-30°C (72-86°F)",
        nutrition: "High in protein (25g per 100g), low in fat, rich in amino acids and iron",
        facts: [
            "Can breathe air using a primitive lung and survive out of water for 4+ hours",
            "Important food fish in Southeast Asia, especially Philippines and Thailand",
            "Believed to have healing properties in traditional medicine",
            "Aggressive predator with excellent parental care - guards eggs and fry",
            "Can move across land to find new water bodies during dry seasons",
            "Has excellent vision and can hunt in murky water"
        ],
        conservation: "Least Concern - highly adaptable and widely distributed",
        cookingTips: "Popular in soups and stews. Firm, white meat with mild flavor. Often used in medicinal soups for recovery."
    },
    tuna: {
        name: "Tuna",
        scientificName: "Thunnus spp.",
        image: "images/tuna.jpg",
        habitat: "Open ocean waters worldwide, from surface to 250m depth",
        diet: "Carnivorous - fish, squid, crustaceans, and cephalopods",
        size: "30-300 cm (1-10 feet) depending on species",
        weight: "1-250 kg (2-550 lbs)",
        lifespan: "15-50+ years",
        waterTemp: "15-25°C (59-77°F)",
        nutrition: "Extremely high in protein (30g per 100g), omega-3 fatty acids, vitamin D, and selenium",
        facts: [
            "Can swim at speeds up to 75 km/h (47 mph) - among fastest fish",
            "Warm-blooded fish that can regulate body temperature",
            "Highly migratory, traveling thousands of miles across oceans",
            "Bluefin tuna can sell for over $1 million at Tokyo fish markets",
            "Have excellent vision and can see in color",
            "Heart-shaped swimming muscles generate tremendous power"
        ],
        conservation: "Varies by species - Atlantic Bluefin is critically endangered, others are overfished",
        cookingTips: "Best served raw (sashimi/sushi) or lightly seared. Rich, meaty flavor. Avoid overcooking."
    },
    mackerel: {
        name: "Mackerel",
        scientificName: "Scomber scombrus",
        image: "images/makarel.jpg",
        habitat: "Coastal and offshore waters, temperate and tropical seas",
        diet: "Carnivorous - small fish, zooplankton, crustaceans, and squid",
        size: "25-60 cm (10-24 inches)",
        weight: "0.5-2 kg (1-4 lbs)",
        lifespan: "17-20 years",
        waterTemp: "8-20°C (46-68°F)",
        nutrition: "Very high in omega-3 fatty acids, vitamin B12, selenium, and vitamin D",
        facts: [
            "Distinctive dark wavy stripes and metallic blue-green back",
            "Forms massive schools of thousands for protection and feeding",
            "Important commercial fish species worldwide",
            "Excellent source of healthy fats - higher omega-3 than salmon",
            "Fast swimmers that can reach speeds of 35 km/h",
            "Migrate seasonally following food sources and temperature"
        ],
        conservation: "Generally stable but some populations face fishing pressure",
        cookingTips: "Rich, oily fish best grilled, smoked, or pickled. Strong flavor pairs well with acidic ingredients."
    },
    catfish: {
        name: "Catfish (Hito)",
        scientificName: "Clarias batrachus",
        image: "images/catfish (hito).jpg",
        habitat: "Freshwater rivers, ponds, rice fields, and muddy waters",
        diet: "Omnivorous - plants, insects, small fish, detritus, and organic matter",
        size: "20-45 cm (8-18 inches)",
        weight: "0.5-2 kg (1-4 lbs)",
        lifespan: "8-20 years",
        waterTemp: "20-30°C (68-86°F)",
        nutrition: "Good source of protein (17g per 100g), vitamin B12, phosphorus, and selenium",
        facts: [
            "Has 4 pairs of whisker-like barbels for sensing food in murky water",
            "Can survive in low-oxygen environments and even out of water briefly",
            "Important aquaculture species across Asia and Africa",
            "Scaleless with smooth, slimy skin that helps with movement",
            "Can breathe air using modified gills when water oxygen is low",
            "Excellent bottom feeder that helps clean aquaculture ponds"
        ],
        conservation: "Least Concern - widely farmed and highly adaptable to various conditions",
        cookingTips: "Popular fried, grilled, or in curries. Firm, white meat with mild flavor. Remove skin before cooking."
    },
    dilis: {
        name: "Dilis (Anchovies)",
        scientificName: "Stolephorus spp.",
        image: "images/dilis (anchovies).jpg",
        habitat: "Coastal marine waters, bays, and estuaries",
        diet: "Planktivorous - zooplankton, phytoplankton, and small organisms",
        size: "5-15 cm (2-6 inches)",
        weight: "5-30 grams",
        lifespan: "2-4 years",
        waterTemp: "24-30°C (75-86°F)",
        nutrition: "High in protein (20g per 100g), calcium, omega-3 fatty acids, and vitamin B12",
        facts: [
            "Form massive schools of millions for protection from predators",
            "Critical base of marine food webs - food for larger fish, seabirds, and marine mammals",
            "Used to make fish sauce (patis) and dried fish (tuyo) in Philippines",
            "Small but extremely nutritionally dense with high calcium content",
            "Short lifespan but reproduce quickly to maintain populations",
            "Filter feeders that help maintain water quality"
        ],
        conservation: "Generally stable but sensitive to climate change and overfishing",
        cookingTips: "Usually dried, salted, or made into fish sauce. Fresh ones can be fried whole or used in soups."
    },
    pufferfish: {
        name: "Pufferfish",
        scientificName: "Tetraodontidae",
        image: "images/pufferfish.jpg",
        habitat: "Tropical and subtropical waters, coral reefs, and coastal areas",
        diet: "Omnivorous - algae, small invertebrates, coral, mollusks, and crustaceans",
        size: "2.5-60 cm (1-24 inches) depending on species",
        weight: "10g-30kg depending on species",
        lifespan: "4-10 years",
        waterTemp: "24-28°C (75-82°F)",
        nutrition: "Contains tetrodotoxin - potentially lethal if not prepared by licensed chefs",
        facts: [
            "Can inflate body to 2-3 times normal size by swallowing water or air",
            "Most species contain tetrodotoxin, one of the most potent neurotoxins",
            "Considered ultimate delicacy (fugu) in Japan - requires special license to prepare",
            "Have beak-like teeth that continuously grow and need wearing down",
            "No stomach - food goes directly from esophagus to intestine",
            "Some species can change color and pattern for camouflage"
        ],
        conservation: "Varies by species - some face habitat loss due to coral reef destruction",
        cookingTips: "DANGEROUS - Only eat if prepared by licensed fugu chef. Extremely toxic if prepared incorrectly."
    },
    salmon: {
        name: "Salmon",
        scientificName: "Salmo salar",
        image: "images/salmon.jpg",
        habitat: "Anadromous - freshwater rivers for spawning, ocean for adult life",
        diet: "Carnivorous - insects and zooplankton (young), fish, squid, and crustaceans (adults)",
        size: "60-150 cm (2-5 feet)",
        weight: "2-30 kg (4-66 lbs)",
        lifespan: "2-8 years",
        waterTemp: "6-14°C (43-57°F)",
        nutrition: "Excellent source of omega-3s (2.3g per 100g), protein (25g), vitamin D, and astaxanthin",
        facts: [
            "Anadromous - born in freshwater, migrate to ocean, return to spawn",
            "Navigate back to exact birthplace using sense of smell",
            "Culturally and economically important to Pacific Northwest peoples",
            "Pink-red flesh color comes from eating krill and shrimp (astaxanthin)",
            "Can jump up to 3.7 meters high to overcome waterfalls",
            "Die after spawning, providing nutrients to forest ecosystems"
        ],
        conservation: "Many wild populations declining due to habitat loss, dams, and climate change",
        cookingTips: "Versatile fish - excellent grilled, baked, smoked, or raw. Rich, buttery flavor with firm texture."
    },
    "maya-maya": {
        name: "Maya-maya (Red Snapper)",
        scientificName: "Lutjanus campechanus",
        image: "https://via.placeholder.com/400x300/ff6b6b/ffffff?text=Maya-maya",
        habitat: "Coral reefs, rocky bottoms, and offshore waters up to 200m depth",
        diet: "Carnivorous - fish, crustaceans, squid, and marine worms",
        size: "35-100 cm (14-40 inches)",
        weight: "2-15 kg (4-33 lbs)",
        lifespan: "20-50+ years",
        waterTemp: "24-28°C (75-82°F)",
        nutrition: "High in protein (26g per 100g), low in fat, rich in selenium and vitamin D",
        facts: [
            "Highly prized fish in Filipino cuisine with sweet, firm white meat",
            "Can live over 50 years and grow quite large",
            "Important commercial and recreational fish species",
            "Changes color from bright red to pink depending on depth",
            "Excellent table fare with delicate, flaky texture",
            "Often confused with other snapper species"
        ],
        conservation: "Overfished in many areas - size and bag limits enforced",
        cookingTips: "Excellent steamed whole, grilled, or fried. Sweet, delicate flavor. Popular in Chinese-style preparations."
    },
    galunggong: {
        name: "Galunggong (Round Scad)",
        scientificName: "Decapterus macrosoma",
        image: "https://via.placeholder.com/400x300/4ecdc4/ffffff?text=Galunggong",
        habitat: "Coastal waters, open ocean, and near coral reefs",
        diet: "Carnivorous - small fish, zooplankton, and crustaceans",
        size: "15-35 cm (6-14 inches)",
        weight: "100-500 grams",
        lifespan: "3-5 years",
        waterTemp: "26-30°C (79-86°F)",
        nutrition: "High in protein (18g per 100g), omega-3 fatty acids, and vitamin B12",
        facts: [
            "One of the most popular and affordable fish in the Philippines",
            "Forms large schools that migrate seasonally",
            "Important source of protein for Filipino families",
            "Has distinctive lateral line and forked tail",
            "Fast-growing species that reaches maturity quickly",
            "Often caught using purse seine nets"
        ],
        conservation: "Stable but subject to seasonal availability",
        cookingTips: "Popular fried, grilled, or in soups. Remove scales before cooking. Mild, slightly oily flavor."
    },
    tanigue: {
        name: "Tanigue (Spanish Mackerel)",
        scientificName: "Scomberomorus commerson",
        image: "https://via.placeholder.com/400x300/667eea/ffffff?text=Tanigue",
        habitat: "Coastal waters, coral reefs, and open ocean",
        diet: "Carnivorous - fish, squid, and crustaceans",
        size: "50-200 cm (20-80 inches)",
        weight: "5-45 kg (11-99 lbs)",
        lifespan: "15-20 years",
        waterTemp: "24-30°C (75-86°F)",
        nutrition: "High in protein (25g per 100g), omega-3 fatty acids, and selenium",
        facts: [
            "Large, fast-swimming predatory fish with excellent eating quality",
            "Can reach speeds up to 60 km/h when hunting",
            "Has distinctive wavy lines and spots on sides",
            "Important game fish for sport fishing",
            "Firm, white meat with rich flavor",
            "Migrates seasonally following baitfish schools"
        ],
        conservation: "Generally stable but some populations face pressure",
        cookingTips: "Excellent grilled, baked, or made into steaks. Rich, meaty flavor. Remove dark meat for milder taste."
    },
    talakitok: {
        name: "Talakitok (Giant Trevally)",
        scientificName: "Caranx ignobilis",
        image: "https://via.placeholder.com/400x300/fd79a8/ffffff?text=Talakitok",
        habitat: "Coral reefs, lagoons, and coastal waters",
        diet: "Carnivorous - fish, crustaceans, and cephalopods",
        size: "50-170 cm (20-67 inches)",
        weight: "5-80 kg (11-176 lbs)",
        lifespan: "15-25 years",
        waterTemp: "24-30°C (75-86°F)",
        nutrition: "High in protein (24g per 100g), low in fat, rich in selenium",
        facts: [
            "Powerful predator known for aggressive feeding behavior",
            "Can grow to massive sizes - largest trevally species",
            "Important in both commercial and recreational fishing",
            "Has distinctive steep forehead and silver coloration",
            "Excellent fighting fish popular with anglers",
            "Forms schools when young, becomes solitary when large"
        ],
        conservation: "Stable but large specimens are becoming rare",
        cookingTips: "Firm, white meat excellent grilled or fried. Mild flavor. Smaller fish are better eating."
    },
    barramundi: {
        name: "Barramundi (Asian Sea Bass)",
        scientificName: "Lates calcarifer",
        image: "https://via.placeholder.com/400x300/00b894/ffffff?text=Barramundi",
        habitat: "Coastal waters, estuaries, and freshwater rivers",
        diet: "Carnivorous - fish, crustaceans, and aquatic insects",
        size: "60-200 cm (24-79 inches)",
        weight: "3-60 kg (7-132 lbs)",
        lifespan: "15-20 years",
        waterTemp: "26-32°C (79-90°F)",
        nutrition: "High in protein (25g per 100g), omega-3 fatty acids, and vitamin D",
        facts: [
            "Catadromous - spawns in saltwater, grows in freshwater",
            "All barramundi are born male and change to female as they mature",
            "Excellent aquaculture species with fast growth",
            "Has large mouth and can swallow prey up to 60% of its length",
            "Prized for its white, flaky meat and mild flavor",
            "Can jump out of water when hooked"
        ],
        conservation: "Stable - widely farmed and sustainably managed",
        cookingTips: "Versatile fish - excellent grilled, baked, or pan-fried. Mild, buttery flavor with firm texture."
    },
    pompano: {
        name: "Pompano",
        scientificName: "Trachinotus carolinus",
        image: "https://via.placeholder.com/400x300/fbbf24/ffffff?text=Pompano",
        habitat: "Coastal waters, sandy beaches, and near-shore areas",
        diet: "Carnivorous - small fish, crustaceans, and marine worms",
        size: "25-60 cm (10-24 inches)",
        weight: "1-4 kg (2-9 lbs)",
        lifespan: "3-4 years",
        waterTemp: "20-28°C (68-82°F)",
        nutrition: "High in protein (23g per 100g), omega-3 fatty acids, and selenium",
        facts: [
            "Considered one of the finest eating fish in the ocean",
            "Has distinctive diamond-shaped, compressed body",
            "Fast-growing species that reaches maturity quickly",
            "Popular target for surf fishing and light tackle",
            "Commands high prices in restaurants",
            "Migrates seasonally along coastlines"
        ],
        conservation: "Generally stable but subject to fishing pressure",
        cookingTips: "Premium eating fish - excellent grilled whole, baked, or pan-seared. Rich, buttery flavor."
    }
};

// Show fish details in modal with full image
function showFishDetails(fishType) {
    const fish = fishData[fishType];
    if (!fish) return;

    const modalContent = document.getElementById('modalContent');
    modalContent.innerHTML = `
        <img src="${fish.image}" alt="${fish.name}" class="modal-fish-image">
        <div class="modal-fish-content">
            <h2 class="modal-fish-title">${fish.name}</h2>
            <p class="modal-scientific-name"><em>${fish.scientificName}</em></p>

            <div class="fish-stats">
                <div class="stat-item">
                    <span class="stat-value">${fish.size}</span>
                    <span class="stat-label">Size Range</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">${fish.weight || 'Varies'}</span>
                    <span class="stat-label">Weight Range</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">${fish.lifespan}</span>
                    <span class="stat-label">Lifespan</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">${fish.waterTemp || 'Varies'}</span>
                    <span class="stat-label">Water Temperature</span>
                </div>
            </div>

            <div class="fish-detail-section">
                <h3>🏠 Natural Habitat</h3>
                <p>${fish.habitat}</p>
            </div>

            <div class="fish-detail-section">
                <h3>🍽️ Diet & Feeding</h3>
                <p>${fish.diet}</p>
            </div>

            <div class="fish-detail-section">
                <h3>🥗 Nutritional Value</h3>
                <p>${fish.nutrition}</p>
            </div>

            <div class="fish-detail-section">
                <h3>🔍 Fascinating Facts</h3>
                <ul>
                    ${fish.facts.map(fact => `<li>${fact}</li>`).join('')}
                </ul>
            </div>

            <div class="fish-detail-section">
                <h3>🌍 Conservation Status</h3>
                <p>${fish.conservation}</p>
            </div>

            ${fish.cookingTips ? `
            <div class="fish-detail-section">
                <h3>👨‍🍳 Cooking Tips</h3>
                <p>${fish.cookingTips}</p>
            </div>
            ` : ''}
        </div>
    `;

    document.getElementById('fishModal').style.display = 'block';
}

// Add CSS for modal content
const modalStyles = `
    .fish-detail-section {
        margin: 1.5rem 0;
    }

    .fish-detail-section h3 {
        color: #2c5aa0;
        margin-bottom: 0.5rem;
        font-size: 1.2rem;
    }

    .fish-detail-section ul {
        padding-left: 1.5rem;
    }

    .fish-detail-section li {
        margin-bottom: 0.5rem;
        line-height: 1.6;
    }

    .scientific-name {
        color: #5a7ba8;
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }

    /* All Fish Modal Styles */
    .all-fish-modal {
        padding: 2rem;
    }

    .all-fish-title {
        font-family: 'Playfair Display', serif;
        font-size: 2.5rem;
        color: var(--deep-ocean);
        text-align: center;
        margin-bottom: 1rem;
        font-weight: 700;
    }

    .all-fish-subtitle {
        text-align: center;
        color: #64748b;
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .all-fish-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .all-fish-card {
        background: linear-gradient(135deg, var(--soft-blue) 0%, var(--coral-light) 100%);
        padding: 1.5rem;
        border-radius: 16px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .all-fish-card:hover {
        transform: translateY(-5px);
        border-color: var(--ocean-blue);
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
    }

    .all-fish-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        filter: drop-shadow(0 0 10px rgba(37, 99, 235, 0.3));
    }

    .all-fish-card h4 {
        color: var(--deep-ocean);
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        font-weight: 700;
        font-family: 'Playfair Display', serif;
    }

    .all-fish-scientific {
        color: var(--ocean-blue);
        font-size: 0.9rem;
        font-style: italic;
        margin-bottom: 1rem;
        opacity: 0.8;
    }

    .view-fish-btn {
        display: inline-block;
        background: linear-gradient(135deg, var(--ocean-blue), var(--tropical-teal));
        color: white;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .all-fish-card:hover .view-fish-btn {
        background: linear-gradient(135deg, var(--tropical-teal), var(--sea-foam));
        transform: translateY(-1px);
    }

    .all-fish-footer {
        text-align: center;
        padding: 1.5rem;
        background: var(--soft-blue);
        border-radius: 12px;
        margin-top: 1rem;
    }

    .all-fish-footer p {
        color: var(--deep-ocean);
        font-size: 1rem;
        margin: 0;
        opacity: 0.8;
    }
`;

// Add styles to head
const styleSheet = document.createElement('style');
styleSheet.textContent = modalStyles;
document.head.appendChild(styleSheet);

// Show all fish function with modal
function showAllFish() {
    const allFishNames = Object.keys(fishData);
    const modalContent = document.getElementById('modalContent');

    let fishCardsHTML = '';
    allFishNames.forEach(fishKey => {
        const fish = fishData[fishKey];
        fishCardsHTML += `
            <div class="all-fish-card" onclick="showFishDetails('${fishKey}')">
                <div class="all-fish-icon">🐟</div>
                <h4>${fish.name}</h4>
                <p class="all-fish-scientific">${fish.scientificName}</p>
                <span class="view-fish-btn">View Details</span>
            </div>
        `;
    });

    modalContent.innerHTML = `
        <div class="all-fish-modal">
            <h2 class="all-fish-title">🌊 All Fish Species</h2>
            <p class="all-fish-subtitle">Explore our complete database of ${allFishNames.length} fish species</p>
            <div class="all-fish-grid">
                ${fishCardsHTML}
            </div>
            <div class="all-fish-footer">
                <p>Click on any fish to view detailed information including habitat, diet, and cooking tips!</p>
            </div>
        </div>
    `;

    document.getElementById('fishModal').style.display = 'block';
}
